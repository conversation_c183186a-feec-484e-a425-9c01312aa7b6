from django import forms
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm
from wms.cores.forms.widget import CoreNumberWidget, CoreSelectWidget
from wms.cores.utils import uom_choices_symbol

from wms.apps.receives.models import GoodsReceivedNoteDefect, GoodsReceivedNoteItem
from wms.apps.settings.models import Warehouse
from decimal import Decimal


class GoodsReceivedNoteDefectForm(CoreModelForm):
    """Form to create GoodsReceivedNoteDefect (reject item)."""

    item_code = forms.CharField(label=_("Item Code"))
    uom = forms.ChoiceField(label=_("UOM"), choices=[])
    reason = forms.ChoiceField(
        label=_("Reject Reason"),
        choices=GoodsReceivedNoteDefect.Reason.choices,
        widget=CoreSelectWidget(attrs={'class': 'w-full'})
    )

    class Meta:
        model = GoodsReceivedNoteDefect
        fields = [
            "item_code",
            "rejected_quantity",
            "uom",
            "reason",
            "batch_no",
            "remark",
            "deliver_to",
            "rejected_by",
            "defect_datetime",
            "item",
            "goods_received_note_item",
        ]
        widgets = {
            "remark": forms.Textarea(attrs={"cols": 80, "rows": 4}),
            "deliver_to": forms.HiddenInput(),
            "rejected_by": forms.HiddenInput(),
            "defect_datetime": forms.HiddenInput(),
            "item": forms.HiddenInput(),
            "goods_received_note_item": forms.HiddenInput(),
            "rejected_quantity": CoreNumberWidget(attrs={'class': 'w-full', 'step': '0.01'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Override label
        self.fields["rejected_quantity"].label = _("Reject Quantity")

        # Add onclick select text
        self.fields["batch_no"].widget.attrs["onClick"] = "this.select();"

        item = self.initial.get("item", None)
        uom = self.initial.get("uom", None)

        # Override UOM choices
        self.fields["uom"].choices = uom_choices_symbol(item=item, pre_selected_uom=uom)

    def clean(self):
        cleaned_data = super().clean()

        item_code = cleaned_data.get("item_code")
        item = cleaned_data.get("item")

        if item_code and item and item_code != item.code:
            msg = _("Item Code does not Match.")
            self.add_error("item_code", msg)

        return cleaned_data

    def save(self, commit=True):
        """
        Save this form's self.instance object if commit=True.
        """
        if self.errors:
            raise ValueError(
                "The %s could not be %s because the data didn't validate."
                % (
                    self.instance._meta.object_name,
                    "created" if self.instance._state.adding else "changed",
                )
            )
        if commit:
            self.instance.save()

        return self.instance
