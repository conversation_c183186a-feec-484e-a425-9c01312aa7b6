import django_tables2 as tables
from django.conf import settings
from django.urls import reverse, NoReverseMatch
from django.utils.translation import gettext_lazy as _

from wms.apps.receives.models import GoodsReceivedNote, GoodsReceivedNoteItem
from wms.cores.columns import HTMXColumn


class GoodsReceivedNoteTable(tables.Table):
    section_title = "Goods Received Notes"

    system_number = tables.LinkColumn(
        "receives:goods_received_notes:panel",
        args=[tables.utils.A("pk")],
        verbose_name=_("Numbering")
    )
    consignor = tables.Column(verbose_name=_("Consignor"))
    customer_reference = tables.Column(verbose_name=_("Customer Reference"))
    status = tables.Column(verbose_name=_("Status"))
    remark = tables.Column(verbose_name=_("Remark"))
    issued_by = tables.Column(verbose_name=_("Issued By"))
    arrival_datetime = tables.DateTimeColumn(
        verbose_name=_("Release Date"),
        format=settings.DATETIME_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )

    created = tables.DateTimeColumn(
        verbose_name=_("Created Date"),
        format=settings.DATETIME_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )

    actions = tables.TemplateColumn(
        verbose_name=_("Actions"),
        template_name="tables/table_actions_column.html",
        orderable=False
    )

    @property
    def create_url(self):
        try:
            return reverse('receives:goods_received_notes:create')
        except NoReverseMatch:
            return None

    class Meta:
        model = GoodsReceivedNote
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"
        fields = (
            "system_number",
            "customer_reference",
            "status",
            "consignor",
            "issued_by",
            "arrival_datetime",
            "created",
        )

    def render_status(self, value, record):
        """Return nice html label display for status."""
        return record.html_status_display

    def render_actions(self, value, record):
        """Actions column to show action buttons."""
        from django.template.loader import get_template

        # Create a context dictionary for the template
        context = {'view_url': "receives:goods_received_notes:panel"}

        if record.status in [GoodsReceivedNote.Status.NEW] and record.all_items_in_new_status:
            context['edit_url'] = "receives:goods_received_notes:update"

        context['record'] = record

        # Render the template with our custom context
        template = get_template("tables/table_actions_column.html")
        return template.render(context)


class GoodsReceivedNoteDetailTable(tables.Table):
    """Table used on goods received note detail page."""

    system_number = HTMXColumn(
        url_name="receives:goods_received_notes:detail_home",
        target_id="detail-panel",
        verbose_name=_("Number"),
        push_url=True,
        push_url_name="receives:goods_received_notes:panel",
    )

    status = tables.Column()

    class Meta:
        model = GoodsReceivedNote
        order_by = '-system_number'
        template_name = "tables/table_htmx.html"
        fields = (
            "system_number",
            "status",
        )

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk

    def render_status(self, value, record):
        """Return nice html label display for status."""
        return record.html_status_display


class GoodsReceivedNoteItemTable(tables.Table):
    """Table for displaying Goods Received Note Items."""
    row_number = tables.Column(
        verbose_name="#",
        empty_values=(),
        orderable=True,
        order_by="sort_order"
    )
    item__code = tables.Column(verbose_name=_("Code"))
    item__name = tables.Column(verbose_name=_("Name"))
    running_number = tables.TemplateColumn(
        verbose_name=_("Number"),
        template_name="receives/partials/goods_received_note_item_running_number.html",
        orderable=False,
    )
    reject_quantity = tables.TemplateColumn(
        verbose_name=_('Reject'),
        template_name="receives/partials/goods_received_note_item_reject_quantity.html",
        orderable=False,
    )
    received_quantity = tables.TemplateColumn(
        verbose_name=_('Received'),
        template_name="receives/partials/goods_received_note_item_received_quantity.html",
        orderable=False,
    )
    quantity = tables.TemplateColumn(
        verbose_name=_('Expected'),
        template_name="receives/partials/goods_received_note_item_quantity.html",
        orderable=False,
    )

    percentage = tables.TemplateColumn(
        verbose_name=_("%"),
        template_name="receives/partials/goods_received_note_item_percentage.html",
        orderable=False,
    )
    batch_no = tables.Column(verbose_name=_("Batch"))
    expiry_date = tables.DateColumn(
        verbose_name=_("Expiry"),
        format=settings.DATE_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )
    uom = tables.Column(verbose_name=_("UOM"))
    # status = tables.TemplateColumn(
    #     verbose_name=_("Status"),
    #     template_name="receives/partials/goods_received_note_item_status.html",
    #     orderable=False,
    #     attrs={
    #         "td": {"class": "status-cell"}
    #     }
    # )
    # action_by = tables.TemplateColumn(
    #     verbose_name=_("Approved"),
    #     template_name="receives/partials/goods_received_note_item_action_by.html",
    #     orderable=False,
    #     attrs={
    #         "td": {"class": "action-by-cell"}
    #     }
    # )

    actions = tables.TemplateColumn(
        verbose_name=_("Actions"),
        template_name="receives/partials/goods_received_note_item_actions.html",
        orderable=False,
    )

    class Meta:
        model = GoodsReceivedNoteItem
        order_by = 'sort_order'
        template_name = "tables/table_htmx.html"
        row_attrs = {
            "id": lambda record: f"item-row-{record.pk}"
        }

        fields = (
            "row_number",
            "item__code",
            "item__name",
            "running_number",
            "percentage",
            "batch_no",
            "expiry_date",
            "reject_quantity",
            "received_quantity",
            "quantity",
            "uom",
            # "status",
            # "action_by",
            "actions"
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.counter = 0

    def render_row_number(self, value, record):
        # Use the get_position method from the model to get the fixed position
        # This ensures the row number stays consistent regardless of sorting
        return str(record.get_position)

    # Status is now rendered using a template column
