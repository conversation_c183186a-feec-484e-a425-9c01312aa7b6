from django.urls import include, path

from wms.apps.receives.views import (
    # GRN
    goods_received_note_list_view,
    goods_received_note_create_view,
    goods_received_note_update_view,
    GoodsReceivedNoteDetailHomeView,
    GoodsReceivedNoteDataTableDetailView,
    GoodsReceivedNoteDetailView,
    GoodsReceivedNoteItemListView,
    goods_received_note_obsolete_form,
    goods_received_note_obsolete,
    goods_received_note_delete_form,
    goods_received_note_delete,
    # GRNItem
    GoodsReceivedNoteItemStockInFormView,
    GoodsReceivedNoteItemAddView,
    GoodsReceivedNoteItemRejectFormView,
    goods_received_note_item_delete_form,
    goods_received_note_item_delete,
    # goods_received_note_item_approve_form,
    # goods_received_note_item_approve,
)

app_name = "receives"

goods_received_notes_urlpatterns = [
    path("", view=goods_received_note_list_view, name="list"),
    path("create/", view=goods_received_note_create_view, name="create"),
    path("<int:pk>/update/", view=goods_received_note_update_view, name="update"),
    path("<int:pk>/", view=GoodsReceivedNoteDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/detail/", view=GoodsReceivedNoteDetailView.as_view(), name="detail"),
    path("<int:pk>/detail-home/", view=GoodsReceivedNoteDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/item/list/", view=GoodsReceivedNoteItemListView.as_view(), name="item_list"),
    # Item actions
    path("item/<int:pk>/receive-form/", view=GoodsReceivedNoteItemStockInFormView.as_view(), name="item_receive_form"),
    path("item/<int:pk>/reject-form/", view=GoodsReceivedNoteItemRejectFormView.as_view(), name="item_reject_form"),
    path("item/<int:pk>/delete-form/", view=goods_received_note_item_delete_form, name="item_delete_form"),
    path("item/<int:pk>/delete/", view=goods_received_note_item_delete, name="item_delete"),
    # path("item/<int:pk>/approve-form/", view=goods_received_note_item_approve_form, name="item_approve_form"),
    # path("item/<int:pk>/approve/", view=goods_received_note_item_approve, name="item_approve"),
    path("<int:pk>/item-add-form/", view=GoodsReceivedNoteItemAddView.as_view(), name="item_add_form"),
    path("<int:pk>/item-add/", view=GoodsReceivedNoteItemAddView.as_view(), name="item_add"),
    # GRN actions
    path("<int:pk>/obsolete-form/", view=goods_received_note_obsolete_form, name="obsolete_form"),
    path("<int:pk>/obsolete/", view=goods_received_note_obsolete, name="obsolete"),
    path("<int:pk>/delete-form/", view=goods_received_note_delete_form, name="delete_form"),
    path("<int:pk>/delete/", view=goods_received_note_delete, name="delete"),
]


# from django.urls import include, path

# from wss.apps.receives.views import (
#     export_all_goods_received_note_to_xlsx_view,
#     goods_received_note_container_detail_view,
#     goods_received_note_container_update_view,
#     goods_received_note_create_view,
#     goods_received_note_datatables_view,
#     goods_received_note_defects_list_view,
#     goods_received_note_delete_view,
#     goods_received_note_detail_datatables_view,
#     goods_received_note_detail_view,
#     goods_received_note_history_list_datatables_view,
#     goods_received_note_history_list_view,
#     goods_received_note_history_modified_view,
#     goods_received_note_import_wizard_view,
#     goods_received_note_info_detail_view,
#     goods_received_note_info_update_view,
#     goods_received_note_is_racking_completed_detail_view,
#     goods_received_note_item_create_view,
#     goods_received_note_item_delete_view,
#     goods_received_note_item_receive_defect_view,
#     goods_received_note_item_receive_view,
#     goods_received_note_item_reject_view,
#     goods_received_note_item_stockins_view,
#     goods_received_note_item_tr_view,
#     goods_received_note_items_list_view,
#     goods_received_note_list_view,
#     goods_received_note_obsolete_view,
#     goods_received_note_print_qr_view,
#     goods_received_note_status_detail_view,
#     goods_received_note_update_view,
#     racking_goods_received_note_datatables_view,
#     racking_goods_received_note_detail_datatables_view,
#     racking_goods_received_note_detail_view,
#     racking_goods_received_note_item_stock_in_view,
#     racking_goods_received_note_item_tr_view,
#     racking_goods_received_note_items_list_view,
#     racking_goods_received_note_list_view,
# )

# app_name = "receives"


# goods_received_notes_urlpatterns = [
#     path("", view=goods_received_note_list_view, name="list"),
#     path("create/", view=goods_received_note_create_view, name="create"),
#     path("import/", view=goods_received_note_import_wizard_view, name="import"),
#     path("delete/<int:pk>/", view=goods_received_note_delete_view, name="delete"),
#     path("detail/<int:pk>/", view=goods_received_note_detail_view, name="detail"),
#     path("detail/<int:pk>/update/", view=goods_received_note_update_view, name="update"),
#     path("detail/<int:pk>/info/", view=goods_received_note_info_detail_view, name="info"),
#     path("detail/<int:pk>/info/update/", view=goods_received_note_info_update_view, name="info_update"),
#     path("detail/<int:pk>/qrcode/", view=goods_received_note_print_qr_view, name="qrcode"),
#     path("detail/<int:pk>/status/", view=goods_received_note_status_detail_view, name="status"),
#     path(
#         "detail/<int:pk>/is-racking-completed/",
#         view=goods_received_note_is_racking_completed_detail_view,
#         name="is-racking-completed",
#     ),
#     path("detail/<int:pk>/container/", view=goods_received_note_container_detail_view, name="container"),
#     path(
#         "detail/<int:pk>/container/update/",
#         view=goods_received_note_container_update_view,
#         name="container_update",
#     ),
#     path("detail/<int:pk>/items/", view=goods_received_note_items_list_view, name="items"),
#     path("detail/<int:pk>/history/", view=goods_received_note_history_list_view, name="history"),
#     path(
#         "detail/<int:pk>/history/popup-modified",
#         view=goods_received_note_history_modified_view,
#         name="history-modified",
#     ),
#     path("detail/<int:pk>/defects/", view=goods_received_note_defects_list_view, name="defects"),
#     path("datatables/", view=goods_received_note_datatables_view, name="datatables"),
#     path("datatables-detail/", view=goods_received_note_detail_datatables_view, name="datatables-detail"),
#     path(
#         "detail/<int:pk>/datatables-history/",
#         view=goods_received_note_history_list_datatables_view,
#         name="datatables-history",
#     ),
#     path("detail/<int:pk>/obsolete/", view=goods_received_note_obsolete_view, name="obsolete"),
#     path("export-all/", view=export_all_goods_received_note_to_xlsx_view, name="export_all"),
# ]

# goods_received_notes_item_urlpatterns = [
#     path("<int:grn_pk>/create/", view=goods_received_note_item_create_view, name="create"),
#     path("delete/<int:pk>/", view=goods_received_note_item_delete_view, name="delete"),
#     path("detail/<int:pk>/stockin/", view=goods_received_note_item_stockins_view, name="stockins"),
#     path("detail/<int:pk>/approve/", view=goods_received_note_item_receive_view, name="approve"),
#     path("detail/<int:pk>/approve-defect/", view=goods_received_note_item_receive_defect_view, name="approve_defect"),
#     path("detail/<int:pk>/defect/", view=goods_received_note_item_reject_view, name="defect"),
#     path("detail/<int:pk>/tr/", view=goods_received_note_item_tr_view, name="tr"),
# ]

# racking_goods_received_notes_urlpatterns = [
#     path("", view=racking_goods_received_note_list_view, name="list"),
#     path("detail/<int:pk>/", view=racking_goods_received_note_detail_view, name="detail"),
#     path("detail/<int:pk>/items/", view=racking_goods_received_note_items_list_view, name="items"),
#     # For GRNItem
#     path("detail/<int:pk>/approve/", view=racking_goods_received_note_item_stock_in_view, name="item-stock-in"),
#     path("detail/<int:pk>/tr/", view=racking_goods_received_note_item_tr_view, name="tr"),
#     # FOR Datatables
#     path("datatables/", view=racking_goods_received_note_datatables_view, name="datatables"),
#     path("datatables-detail/", view=racking_goods_received_note_detail_datatables_view, name="datatables-detail"),
# ]
urlpatterns = [
    path(
        "goods-received-notes/",
        include((goods_received_notes_urlpatterns, "receives.goods_received_note"), namespace="goods_received_notes"),
    ),
#     path(
#         "goods-received-notes-item/",
#         include(
#             (goods_received_notes_item_urlpatterns, "receives.goods_received_note_item"),
#             namespace="goods_received_notes_item",
#         ),
#     ),
#     path(
#         "rackings/",
#         include(
#             (racking_goods_received_notes_urlpatterns, "receives.racking_goods_received_note"),
#             namespace="racking_goods_received_notes",
#         ),
#     ),
]
