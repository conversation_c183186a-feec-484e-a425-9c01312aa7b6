from .goods_received_note import (
    goods_received_note_list_view,
    goods_received_note_create_view,
    goods_received_note_update_view,
    GoodsReceivedNoteDetailHomeView,
    GoodsReceivedNoteDetailView,
    GoodsReceivedNoteDataTableDetailView,
    GoodsReceivedNoteItemListView,
    goods_received_note_obsolete_form,
    goods_received_note_obsolete,
    goods_received_note_delete_form,
    goods_received_note_delete,

# TO BE REMOVED
#     export_all_goods_received_note_to_xlsx_view,
#     goods_received_note_container_detail_view,
#     goods_received_note_container_update_view,
#     goods_received_note_create_view,
#     goods_received_note_datatables_view,
#     goods_received_note_defects_list_view,
#     goods_received_note_delete_view,
#     goods_received_note_detail_datatables_view,
#     goods_received_note_detail_view,
#     goods_received_note_history_list_datatables_view,
#     goods_received_note_history_list_view,
#     goods_received_note_history_modified_view,
#     goods_received_note_import_wizard_view,
#     goods_received_note_info_detail_view,
#     goods_received_note_info_update_view,
#     goods_received_note_is_racking_completed_detail_view,
#     goods_received_note_items_list_view,
#     goods_received_note_list_view,
#     goods_received_note_obsolete_view,
#     goods_received_note_print_qr_view,
#     goods_received_note_status_detail_view,
#     goods_received_note_update_view,
)
from .goods_received_note_item import (
    GoodsReceivedNoteItemStockInFormView,
    GoodsReceivedNoteItemAddView,
    GoodsReceivedNoteItemRejectFormView,
    goods_received_note_item_delete_form,
    goods_received_note_item_delete,

# TO BE REMOVED
#     goods_received_note_item_create_view,
#     goods_received_note_item_delete_view,
#     goods_received_note_item_receive_defect_view,
#     goods_received_note_item_receive_view,
#     goods_received_note_item_reject_view,
#     goods_received_note_item_stockins_view,
#     goods_received_note_item_tr_view,
)

# TO BE REMOVED
# from .racking_stock_in import (
#     racking_goods_received_note_datatables_view,
#     racking_goods_received_note_detail_datatables_view,
#     racking_goods_received_note_detail_view,
#     racking_goods_received_note_item_stock_in_view,
#     racking_goods_received_note_item_tr_view,
#     racking_goods_received_note_items_list_view,
#     racking_goods_received_note_list_view,
# )

__all__ = [
    # GRN
    "goods_received_note_list_view",
    "goods_received_note_create_view",
    "goods_received_note_update_view",
    "goods_received_note_obsolete_form",
    "goods_received_note_obsolete",
    "goods_received_note_delete_form",
    "goods_received_note_delete",
    # GRN ITEM
    "GoodsReceivedNoteItemStockInFormView",
    "GoodsReceivedNoteItemAddView",
    "goods_received_note_item_delete_form",
    "goods_received_note_item_delete",
# TO BE REMOVED
#     "racking_goods_received_note_item_tr_view",
#     "racking_goods_received_note_item_stock_in_view",
#     "racking_goods_received_note_items_list_view",
#     "racking_goods_received_note_detail_datatables_view",
#     "racking_goods_received_note_detail_view",
#     "racking_goods_received_note_datatables_view",
#     "racking_goods_received_note_list_view",
#     "goods_received_note_container_detail_view",
#     "goods_received_note_container_update_view",
#     "goods_received_note_create_view",
#     "goods_received_note_datatables_view",
#     "goods_received_note_defects_list_view",
#     "goods_received_note_delete_view",
#     "goods_received_note_detail_datatables_view",
#     "goods_received_note_detail_view",
#     "goods_received_note_history_list_datatables_view",
#     "goods_received_note_history_list_view",
#     "goods_received_note_history_modified_view",
#     "goods_received_note_import_wizard_view",
#     "goods_received_note_info_detail_view",
#     "goods_received_note_info_update_view",
#     "goods_received_note_item_create_view",
#     "goods_received_note_item_delete_view",
#     "goods_received_note_item_receive_view",
#     "goods_received_note_item_receive_defect_view",
#     "goods_received_note_item_reject_view",
#     "goods_received_note_item_stockins_view",
#     "goods_received_note_item_tr_view",
#     "goods_received_note_items_list_view",
#     "goods_received_note_list_view",
#     "goods_received_note_obsolete_view",
#     "goods_received_note_status_detail_view",
#     "goods_received_note_update_view",
#     "goods_received_note_print_qr_view",
#     "export_all_goods_received_note_to_xlsx_view",
#     "goods_received_note_is_racking_completed_detail_view",
]
