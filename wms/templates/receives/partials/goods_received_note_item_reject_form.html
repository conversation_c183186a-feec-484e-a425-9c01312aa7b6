{% load i18n %}
{% load crispy_forms_tags %}
{% load humanize %}

<form
  method="post"
  hx-post="{% url 'receives:goods_received_notes:item_reject_form' goods_received_note_item.pk %}"
  hx-trigger="submit"
>
  {% csrf_token %}
  <div class="p-0">
    <!-- Item Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-2 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Item Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Item Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-4">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Code" %}</label>
            <div class="mt-1 text-sm text-gray-900 font-semibold">{{ goods_received_note_item.item.code }}</div>
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Name" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.item.name }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Batch No" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.batch_no|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Expiry Date" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.expiry_date|date:"d M Y"|default:"-" }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Expected Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900 font-semibold">{{ goods_received_note_item.quantity|floatformat:0|intcomma }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "UOM" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.uom.symbol }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Received Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.get_received_quantity|floatformat:0|intcomma }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Rejected Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.get_defect_converted_rejected_quantity|floatformat:0|intcomma }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reject Form Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-2 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Reject Information" %}</h4>
      </div>
      <div class="p-4">
        <div class="grid grid-cols-1 gap-4">
          <!-- Reject Quantity -->
          <div>
            {{ form.rejected_quantity|as_crispy_field }}
          </div>

          <!-- Reject Reason -->
          <div>
            {{ form.reason|as_crispy_field }}
          </div>

          <!-- Remarks -->
          <div>
            {{ form.remark|as_crispy_field }}
          </div>
        </div>
      </div>
    </div>

    <!-- Hidden Fields -->
    {{ form.item_code|as_crispy_field }}
    {{ form.uom|as_crispy_field }}
    {{ form.batch_no|as_crispy_field }}
    {{ form.deliver_to|as_crispy_field }}
    {{ form.rejected_by|as_crispy_field }}
    {{ form.defect_datetime|as_crispy_field }}
    {{ form.item|as_crispy_field }}
    {{ form.goods_received_note_item|as_crispy_field }}

    <!-- Action Buttons -->
    <div class="flex justify-between space-x-3">
      <button
        type="button"
        class="py-1.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
        @click="$dispatch('close-modal')"
      >
        {% translate "Cancel" %}
      </button>
      <button
        type="submit"
        class="py-1.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
      >
        {% translate "Reject Item" %}
      </button>
    </div>
  </div>
</form>

<script>
  (function() {
    // Function to position cursor at the end of the quantity field
    function positionCursorAtEnd() {
      const quantityField = document.getElementById('id_rejected_quantity');
      if (quantityField) {
        quantityField.focus();
        const valueLength = quantityField.value.length;
        quantityField.type = 'text';
        quantityField.setSelectionRange(valueLength, valueLength);
        quantityField.type = 'number';
      }
    }

    // Initialize and set up event listeners
    document.addEventListener('htmx:afterSettle', function() {
      const quantityField = document.getElementById('id_rejected_quantity');
      if (quantityField) {
        positionCursorAtEnd();
      }
    });
  })();
</script>
